<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BuildMachineOSBuild</key>
	<string>20F71</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleExecutable</key>
	<string>TXLiteAVSDK_ReplayKitExt</string>
	<key>CFBundleIdentifier</key>
	<string>com.tencent.TXLiteAVSDK.ReplayKitExt</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>TXLiteAVSDK_ReplayKitExt</string>
	<key>CFBundlePackageType</key>
	<string>FMWK</string>
	<key>CFBundleShortVersionString</key>
	<string>9.9.0.11217</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleSupportedPlatforms</key>
	<array>
		<string>iPhoneSimulator</string>
	</array>
	<key>CFBundleVersion</key>
	<string>1.0</string>
	<key>DTCompiler</key>
	<string>com.apple.compilers.llvm.clang.1_0</string>
	<key>DTPlatformBuild</key>
	<string></string>
	<key>DTPlatformName</key>
	<string>iphonesimulator</string>
	<key>DTPlatformVersion</key>
	<string>14.5</string>
	<key>DTSDKBuild</key>
	<string>18E182</string>
	<key>DTSDKName</key>
	<string>iphonesimulator14.5</string>
	<key>DTXcode</key>
	<string>1250</string>
	<key>DTXcodeBuild</key>
	<string>12E262</string>
	<key>MinimumOSVersion</key>
	<string>9.0</string>
	<key>NSPrincipalClass</key>
	<string></string>
	<key>UIDeviceFamily</key>
	<array>
		<integer>1</integer>
		<integer>2</integer>
	</array>
</dict>
</plist>
