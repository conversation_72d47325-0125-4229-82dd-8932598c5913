<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
	<array>
		<string>cn.examplecode.flutterProviderMvvm.shicheng</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>zh_CN</string>
	<key>CFBundleDisplayName</key>
	<string>青桐智盒</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>notarization_station_app</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleTypeRole</key>
	<string>Editor</string>
	<key>CFBundleURLName</key>
	<string>alipay</string>
	<key>CFBundleURLSchemes</key>
	<array>
		<string>tobiasexample</string>
	</array>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>alipay</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>sc.njguochu.com</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>weixin</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wx01962cc4ae161f91</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>qingtongzhihe</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>njguochu</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>weixin</string>
		<string>weixinULAPI</string>
		<string>wechat</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSAppleMusicUsageDescription</key>
	<string>App需要您的同意,才能访问音乐权限，以便在视频公证时使用。</string>
	<key>NSCalendarsUsageDescription</key>
	<string>App需要您的同意,才能访问日历，以便公证时使用。</string>
	<key>NSCameraUsageDescription</key>
	<string>App需要您的同意,才能访问您的相机，以便在自助办证、上传头像等其他功能时拍摄照片。</string>
	<key>NSContactsUsageDescription</key>
	<string>App需要您的同意,才能访问通讯录，以便拨打电话。</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>App需要您的同意,才能访问您的地址权限，以便在自助办证、视频公证时获取公证处信息。</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>App需要您的同意,才能访问您的地址权限，以便在自助办证、视频公证时获取公证处信息。</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>App需要您的同意,才能访问您的麦克风，以便在视频公证时使用。</string>
	<key>NSMotionUsageDescription</key>
	<string>App需要您的同意,才能访问运动权限，以便在视频公证时使用。</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>App需要您的同意,才能访问您的相册，以便在自助办证、上传头像等其他功能时使用图片。</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>App需要您的同意,才能访问您的相册，以便在自助办证、上传头像等其他功能时使用图片。</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>App需要您的同意,才能访问您的语音识别权限，以便在视频公证时使用。</string>
	<key>UIBackgroundModes</key>
	<array/>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>io.flutter.embedded_views_preview</key>
	<true/>
</dict>
</plist>
