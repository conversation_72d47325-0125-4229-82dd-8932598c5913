group 'com.isvisoft.flutter_screen_recording'
version '1.0-SNAPSHOT'

buildscript {
    ext.kotlin_version = '1.6.0'
    repositories {
        google()
        jcenter()
        maven { url 'https://jitpack.io' }

    }

    dependencies {
        classpath 'com.android.tools.build:gradle:3.5.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

rootProject.allprojects {
    repositories {
        google()
        jcenter()
        maven { url 'https://jitpack.io' }
    }
}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    compileSdkVersion 32

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }
    defaultConfig {
        minSdkVersion 21
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    lintOptions {
        disable 'InvalidPackage'
    }
    compileOptions {
        targetCompatibility JavaVersion.VERSION_1_8
    }
    android {
        kotlinOptions {
            jvmTarget = '1.8'
        }
    }
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'androidx.appcompat:appcompat:1.0.0'
    implementation 'com.github.HBiSoft:HBRecorder:2.0.5'

}
