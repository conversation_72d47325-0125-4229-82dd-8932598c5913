// ignore_for_file: non_constant_identifier_names
// ignore_for_file: camel_case_types
// ignore_for_file: prefer_single_quotes

import 'package:notarization_station_app/generated/json/address_entity_helper.dart';
import 'package:notarization_station_app/generated/json/contract_entity_helper.dart';
import 'package:notarization_station_app/generated/json/information_entity_helper.dart';
import 'package:notarization_station_app/generated/json/invitee_info_entity_helper.dart';
import 'package:notarization_station_app/generated/json/notarial_office_entity_helper.dart';
import 'package:notarization_station_app/generated/json/order_info_entity_helper.dart';
import 'package:notarization_station_app/generated/json/small_list_entity_helper.dart';
import 'package:notarization_station_app/generated/json/small_material_entity_helper.dart';
import 'package:notarization_station_app/generated/json/upload_img_entity_helper.dart';
import 'package:notarization_station_app/generated/json/user_info_entity_helper.dart';
import 'package:notarization_station_app/generated/json/video_entity_helper.dart';
import 'package:notarization_station_app/generated/json/vote_entity_helper.dart';
import 'package:notarization_station_app/generated/json/widget_map_entity_helper.dart';
import 'package:notarization_station_app/page/home/<USER>/contract_entity.dart';
import 'package:notarization_station_app/page/home/<USER>/invitee_info_entity.dart';
import 'package:notarization_station_app/page/home/<USER>/notarial_office_entity.dart';
import 'package:notarization_station_app/page/home/<USER>/order_info_entity.dart';
import 'package:notarization_station_app/page/home/<USER>/small_list_entity.dart';
import 'package:notarization_station_app/page/home/<USER>/small_material_entity.dart';
import 'package:notarization_station_app/page/home/<USER>/video_entity.dart';
import 'package:notarization_station_app/page/home/<USER>/vote_entity.dart';
// This file is automatically generated. DO NOT EDIT, all your changes would be lost.
import 'package:notarization_station_app/page/home/<USER>/widget_map_entity.dart';
import 'package:notarization_station_app/page/infomation/entity/information_entity.dart';
import 'package:notarization_station_app/page/login/entity/user_info_entity.dart';
import 'package:notarization_station_app/page/mine/entity/address_entity.dart';
import 'package:notarization_station_app/page/mine/entity/upload_img_entity.dart';

class JsonConvert<T> {
  T fromJson(Map<String, dynamic> json) {
    return _getFromJson<T>(runtimeType, this, json);
  }

  Map<String, dynamic> toJson() {
    return _getToJson<T>(runtimeType, this);
  }

  static _getFromJson<T>(Type type, data, json) {
    switch (type) {
      case WidgetMapEntity:
        return widgetMapEntityFromJson(data as WidgetMapEntity, json) as T;
      case VideoEntity:
        return videoEntityFromJson(data as VideoEntity, json) as T;
      case VideoUnitGuid:
        return videoUnitGuidFromJson(data as VideoUnitGuid, json) as T;
      case UserInfoEntity:
        return userInfoEntityFromJson(data as UserInfoEntity, json) as T;
      case UserInfoItems:
        return userInfoItemsFromJson(data as UserInfoItems, json) as T;
      case UserInfoItemsUser:
        return userInfoItemsUserFromJson(data as UserInfoItemsUser, json) as T;
      case UploadImgEntity:
        return uploadImgEntityFromJson(data as UploadImgEntity, json) as T;
      case UploadImgItem:
        return uploadImgItemFromJson(data as UploadImgItem, json) as T;
      case ContractEntity:
        return contractEntityFromJson(data as ContractEntity, json) as T;
      case VoteEntity:
        return voteEntityFromJson(data as VoteEntity, json) as T;
      case VoteVoteData:
        return voteVoteDataFromJson(data as VoteVoteData, json) as T;
      case InformationEntity:
        return informationEntityFromJson(data as InformationEntity, json) as T;
      case InformationPage:
        return informationPageFromJson(data as InformationPage, json) as T;
      case InformationItem:
        return informationItemFromJson(data as InformationItem, json) as T;
      case SmallListEntity:
        return smallListEntityFromJson(data as SmallListEntity, json) as T;
      case SmallListPage:
        return smallListPageFromJson(data as SmallListPage, json) as T;
      case SmallListItems:
        return smallListItemsFromJson(data as SmallListItems, json) as T;
      case SmallListItemsUserList:
        return smallListItemsUserListFromJson(
            data as SmallListItemsUserList, json) as T;
      case SmallListItemsImgPath:
        return smallListItemsImgPathFromJson(
            data as SmallListItemsImgPath, json) as T;
      case SmallListItemsHtml:
        return smallListItemsHtmlFromJson(data as SmallListItemsHtml, json)
            as T;
      case InviteeInfoEntity:
        return inviteeInfoEntityFromJson(data as InviteeInfoEntity, json) as T;
      case SmallMaterialEntity:
        return smallMaterialEntityFromJson(data as SmallMaterialEntity, json)
            as T;
      case SmallMaterialData:
        return smallMaterialDataFromJson(data as SmallMaterialData, json) as T;
      case AddressEntity:
        return addressEntityFromJson(data as AddressEntity, json) as T;
      case AddressPage:
        return addressPageFromJson(data as AddressPage, json) as T;
      case AddressItem:
        return addressItemFromJson(data as AddressItem, json) as T;
      case OrderInfoEntity:
        return orderInfoEntityFromJson(data as OrderInfoEntity, json) as T;
      case OrderInfoItem:
        return orderInfoItemFromJson(data as OrderInfoItem, json) as T;
      case OrderInfoItemNotaryItem:
        return orderInfoItemNotaryItemFromJson(
            data as OrderInfoItemNotaryItem, json) as T;
      case OrderInfoItemOrderLog:
        return orderInfoItemOrderLogFromJson(
            data as OrderInfoItemOrderLog, json) as T;
      case OrderInfoItemUser:
        return orderInfoItemUserFromJson(data as OrderInfoItemUser, json) as T;
      case OrderInfoItemApplyuser:
        return orderInfoItemApplyuserFromJson(
            data as OrderInfoItemApplyuser, json) as T;
      case OrderInfoItemOrder:
        return orderInfoItemOrderFromJson(data as OrderInfoItemOrder, json)
            as T;
      case NotarialOfficeEntity:
        return notarialOfficeEntityFromJson(data as NotarialOfficeEntity, json)
            as T;
      case NotarialOfficeItem:
        return notarialOfficeItemFromJson(data as NotarialOfficeItem, json)
            as T;
    }
    return data as T;
  }

  static _getToJson<T>(Type type, data) {
    switch (type) {
      case WidgetMapEntity:
        return widgetMapEntityToJson(data as WidgetMapEntity);
      case VideoEntity:
        return videoEntityToJson(data as VideoEntity);
      case VideoUnitGuid:
        return videoUnitGuidToJson(data as VideoUnitGuid);
      case UserInfoEntity:
        return userInfoEntityToJson(data as UserInfoEntity);
      case UserInfoItems:
        return userInfoItemsToJson(data as UserInfoItems);
      case UserInfoItemsUser:
        return userInfoItemsUserToJson(data as UserInfoItemsUser);
      case UploadImgEntity:
        return uploadImgEntityToJson(data as UploadImgEntity);
      case UploadImgItem:
        return uploadImgItemToJson(data as UploadImgItem);
      case ContractEntity:
        return contractEntityToJson(data as ContractEntity);
      case VoteEntity:
        return voteEntityToJson(data as VoteEntity);
      case VoteVoteData:
        return voteVoteDataToJson(data as VoteVoteData);
      case InformationEntity:
        return informationEntityToJson(data as InformationEntity);
      case InformationPage:
        return informationPageToJson(data as InformationPage);
      case InformationItem:
        return informationItemToJson(data as InformationItem);
      case SmallListEntity:
        return smallListEntityToJson(data as SmallListEntity);
      case SmallListPage:
        return smallListPageToJson(data as SmallListPage);
      case SmallListItems:
        return smallListItemsToJson(data as SmallListItems);
      case SmallListItemsUserList:
        return smallListItemsUserListToJson(data as SmallListItemsUserList);
      case SmallListItemsImgPath:
        return smallListItemsImgPathToJson(data as SmallListItemsImgPath);
      case SmallListItemsHtml:
        return smallListItemsHtmlToJson(data as SmallListItemsHtml);
      case InviteeInfoEntity:
        return inviteeInfoEntityToJson(data as InviteeInfoEntity);
      case SmallMaterialEntity:
        return smallMaterialEntityToJson(data as SmallMaterialEntity);
      case SmallMaterialData:
        return smallMaterialDataToJson(data as SmallMaterialData);
      case AddressEntity:
        return addressEntityToJson(data as AddressEntity);
      case AddressPage:
        return addressPageToJson(data as AddressPage);
      case AddressItem:
        return addressItemToJson(data as AddressItem);
      case OrderInfoEntity:
        return orderInfoEntityToJson(data as OrderInfoEntity);
      case OrderInfoItem:
        return orderInfoItemToJson(data as OrderInfoItem);
      case OrderInfoItemNotaryItem:
        return orderInfoItemNotaryItemToJson(data as OrderInfoItemNotaryItem);
      case OrderInfoItemOrderLog:
        return orderInfoItemOrderLogToJson(data as OrderInfoItemOrderLog);
      case OrderInfoItemUser:
        return orderInfoItemUserToJson(data as OrderInfoItemUser);
      case OrderInfoItemApplyuser:
        return orderInfoItemApplyuserToJson(data as OrderInfoItemApplyuser);
      case OrderInfoItemOrder:
        return orderInfoItemOrderToJson(data as OrderInfoItemOrder);
      case NotarialOfficeEntity:
        return notarialOfficeEntityToJson(data as NotarialOfficeEntity);
      case NotarialOfficeItem:
        return notarialOfficeItemToJson(data as NotarialOfficeItem);
    }
    return data as T;
  }

  //Go back to a single instance by type
  static _fromJsonSingle<M>(json) {
    String type = M.toString();
    if (type == (WidgetMapEntity).toString()) {
      return WidgetMapEntity().fromJson(json);
    } else if (type == (VideoEntity).toString()) {
      return VideoEntity().fromJson(json);
    } else if (type == (VideoUnitGuid).toString()) {
      return VideoUnitGuid().fromJson(json);
    } else if (type == (UserInfoEntity).toString()) {
      return UserInfoEntity().fromJson(json);
    } else if (type == (UserInfoItems).toString()) {
      return UserInfoItems().fromJson(json);
    } else if (type == (UserInfoItemsUser).toString()) {
      return UserInfoItemsUser().fromJson(json);
    } else if (type == (UploadImgEntity).toString()) {
      return UploadImgEntity().fromJson(json);
    } else if (type == (UploadImgItem).toString()) {
      return UploadImgItem().fromJson(json);
    } else if (type == (ContractEntity).toString()) {
      return ContractEntity().fromJson(json);
    } else if (type == (VoteEntity).toString()) {
      return VoteEntity().fromJson(json);
    } else if (type == (VoteVoteData).toString()) {
      return VoteVoteData().fromJson(json);
    } else if (type == (InformationEntity).toString()) {
      return InformationEntity().fromJson(json);
    } else if (type == (InformationPage).toString()) {
      return InformationPage().fromJson(json);
    } else if (type == (InformationItem).toString()) {
      return InformationItem().fromJson(json);
    } else if (type == (SmallListEntity).toString()) {
      return SmallListEntity().fromJson(json);
    } else if (type == (SmallListPage).toString()) {
      return SmallListPage().fromJson(json);
    } else if (type == (SmallListItems).toString()) {
      return SmallListItems().fromJson(json);
    } else if (type == (SmallListItemsUserList).toString()) {
      return SmallListItemsUserList().fromJson(json);
    } else if (type == (SmallListItemsImgPath).toString()) {
      return SmallListItemsImgPath().fromJson(json);
    } else if (type == (SmallListItemsHtml).toString()) {
      return SmallListItemsHtml().fromJson(json);
    } else if (type == (InviteeInfoEntity).toString()) {
      return InviteeInfoEntity().fromJson(json);
    } else if (type == (SmallMaterialEntity).toString()) {
      return SmallMaterialEntity().fromJson(json);
    } else if (type == (SmallMaterialData).toString()) {
      return SmallMaterialData().fromJson(json);
    } else if (type == (AddressEntity).toString()) {
      return AddressEntity().fromJson(json);
    } else if (type == (AddressPage).toString()) {
      return AddressPage().fromJson(json);
    } else if (type == (AddressItem).toString()) {
      return AddressItem().fromJson(json);
    } else if (type == (OrderInfoEntity).toString()) {
      return OrderInfoEntity().fromJson(json);
    } else if (type == (OrderInfoItem).toString()) {
      return OrderInfoItem().fromJson(json);
    } else if (type == (OrderInfoItemNotaryItem).toString()) {
      return OrderInfoItemNotaryItem().fromJson(json);
    } else if (type == (OrderInfoItemOrderLog).toString()) {
      return OrderInfoItemOrderLog().fromJson(json);
    } else if (type == (OrderInfoItemUser).toString()) {
      return OrderInfoItemUser().fromJson(json);
    } else if (type == (OrderInfoItemApplyuser).toString()) {
      return OrderInfoItemApplyuser().fromJson(json);
    } else if (type == (OrderInfoItemOrder).toString()) {
      return OrderInfoItemOrder().fromJson(json);
    } else if (type == (NotarialOfficeEntity).toString()) {
      return NotarialOfficeEntity().fromJson(json);
    } else if (type == (NotarialOfficeItem).toString()) {
      return NotarialOfficeItem().fromJson(json);
    }
    return null;
  }

  //list is returned by type
  static M _getListChildType<M>(List data) {
    if (<WidgetMapEntity>[] is M) {
      return data
          .map<WidgetMapEntity>((e) => WidgetMapEntity().fromJson(e))
          .toList() as M;
    } else if (<VideoEntity>[] is M) {
      return data.map<VideoEntity>((e) => VideoEntity().fromJson(e)).toList()
          as M;
    } else if (<VideoUnitGuid>[] is M) {
      return data
          .map<VideoUnitGuid>((e) => VideoUnitGuid().fromJson(e))
          .toList() as M;
    } else if (<UserInfoEntity>[] is M) {
      return data
          .map<UserInfoEntity>((e) => UserInfoEntity().fromJson(e))
          .toList() as M;
    } else if (<UserInfoItems>[] is M) {
      return data
          .map<UserInfoItems>((e) => UserInfoItems().fromJson(e))
          .toList() as M;
    } else if (<UserInfoItemsUser>[] is M) {
      return data
          .map<UserInfoItemsUser>((e) => UserInfoItemsUser().fromJson(e))
          .toList() as M;
    } else if (<UploadImgEntity>[] is M) {
      return data
          .map<UploadImgEntity>((e) => UploadImgEntity().fromJson(e))
          .toList() as M;
    } else if (<UploadImgItem>[] is M) {
      return data
          .map<UploadImgItem>((e) => UploadImgItem().fromJson(e))
          .toList() as M;
    } else if (<ContractEntity>[] is M) {
      return data
          .map<ContractEntity>((e) => ContractEntity().fromJson(e))
          .toList() as M;
    } else if (<VoteEntity>[] is M) {
      return data.map<VoteEntity>((e) => VoteEntity().fromJson(e)).toList()
          as M;
    } else if (<VoteVoteData>[] is M) {
      return data.map<VoteVoteData>((e) => VoteVoteData().fromJson(e)).toList()
          as M;
    } else if (<InformationEntity>[] is M) {
      return data
          .map<InformationEntity>((e) => InformationEntity().fromJson(e))
          .toList() as M;
    } else if (<InformationPage>[] is M) {
      return data
          .map<InformationPage>((e) => InformationPage().fromJson(e))
          .toList() as M;
    } else if (<InformationItem>[] is M) {
      return data
          .map<InformationItem>((e) => InformationItem().fromJson(e))
          .toList() as M;
    } else if (<SmallListEntity>[] is M) {
      return data
          .map<SmallListEntity>((e) => SmallListEntity().fromJson(e))
          .toList() as M;
    } else if (<SmallListPage>[] is M) {
      return data
          .map<SmallListPage>((e) => SmallListPage().fromJson(e))
          .toList() as M;
    } else if (<SmallListItems>[] is M) {
      return data
          .map<SmallListItems>((e) => SmallListItems().fromJson(e))
          .toList() as M;
    } else if (<SmallListItemsUserList>[] is M) {
      return data
          .map<SmallListItemsUserList>(
              (e) => SmallListItemsUserList().fromJson(e))
          .toList() as M;
    } else if (<SmallListItemsImgPath>[] is M) {
      return data
          .map<SmallListItemsImgPath>(
              (e) => SmallListItemsImgPath().fromJson(e))
          .toList() as M;
    } else if (<SmallListItemsHtml>[] is M) {
      return data
          .map<SmallListItemsHtml>((e) => SmallListItemsHtml().fromJson(e))
          .toList() as M;
    } else if (<InviteeInfoEntity>[] is M) {
      return data
          .map<InviteeInfoEntity>((e) => InviteeInfoEntity().fromJson(e))
          .toList() as M;
    } else if (<SmallMaterialEntity>[] is M) {
      return data
          .map<SmallMaterialEntity>((e) => SmallMaterialEntity().fromJson(e))
          .toList() as M;
    } else if (<SmallMaterialData>[] is M) {
      return data
          .map<SmallMaterialData>((e) => SmallMaterialData().fromJson(e))
          .toList() as M;
    } else if (<AddressEntity>[] is M) {
      return data
          .map<AddressEntity>((e) => AddressEntity().fromJson(e))
          .toList() as M;
    } else if (<AddressPage>[] is M) {
      return data.map<AddressPage>((e) => AddressPage().fromJson(e)).toList()
          as M;
    } else if (<AddressItem>[] is M) {
      return data.map<AddressItem>((e) => AddressItem().fromJson(e)).toList()
          as M;
    } else if (<OrderInfoEntity>[] is M) {
      return data
          .map<OrderInfoEntity>((e) => OrderInfoEntity().fromJson(e))
          .toList() as M;
    } else if (<OrderInfoItem>[] is M) {
      return data
          .map<OrderInfoItem>((e) => OrderInfoItem().fromJson(e))
          .toList() as M;
    } else if (<OrderInfoItemNotaryItem>[] is M) {
      return data
          .map<OrderInfoItemNotaryItem>(
              (e) => OrderInfoItemNotaryItem().fromJson(e))
          .toList() as M;
    } else if (<OrderInfoItemOrderLog>[] is M) {
      return data
          .map<OrderInfoItemOrderLog>(
              (e) => OrderInfoItemOrderLog().fromJson(e))
          .toList() as M;
    } else if (<OrderInfoItemUser>[] is M) {
      return data
          .map<OrderInfoItemUser>((e) => OrderInfoItemUser().fromJson(e))
          .toList() as M;
    } else if (<OrderInfoItemApplyuser>[] is M) {
      return data
          .map<OrderInfoItemApplyuser>(
              (e) => OrderInfoItemApplyuser().fromJson(e))
          .toList() as M;
    } else if (<OrderInfoItemOrder>[] is M) {
      return data
          .map<OrderInfoItemOrder>((e) => OrderInfoItemOrder().fromJson(e))
          .toList() as M;
    } else if (<NotarialOfficeEntity>[] is M) {
      return data
          .map<NotarialOfficeEntity>((e) => NotarialOfficeEntity().fromJson(e))
          .toList() as M;
    } else if (<NotarialOfficeItem>[] is M) {
      return data
          .map<NotarialOfficeItem>((e) => NotarialOfficeItem().fromJson(e))
          .toList() as M;
    }
    throw Exception("not fond");
  }

  static M fromJsonAsT<M>(json) {
    if (json is List) {
      return _getListChildType<M>(json);
    } else {
      return _fromJsonSingle<M>(json) as M;
    }
  }
}
