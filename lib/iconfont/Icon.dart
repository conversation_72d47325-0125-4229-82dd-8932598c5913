import 'package:flutter/material.dart';

//图标使用
//二维码识别
Icon codeIco({double size = 18.0, Color color}) => Icon(
      Icons.crop_free,
      size: size,
      color: color,
    );
//首页
Icon homeIco({double size = 18.0, Color color}) => Icon(
      const IconData(0xe653, fontFamily: 'iconfont'),
      size: size,
      color: color,
    );

//公证记录
Icon recordIco({double size = 18.0, Color color}) => Icon(
      const IconData(0xe791, fontFamily: 'iconfont'),
      size: size,
      color: color,
    );

//在线公证
Icon notarizationIco({double size = 18.0, Color color}) => Icon(
      const IconData(0xe6fc, fontFamily: 'iconfont'),
      size: size,
      color: color,
    );

//个人中心
Icon mineIco({double size = 18.0, Color color}) => Icon(
      const IconData(0xe60e, fontFamily: 'iconfont'),
      size: size,
      color: color,
    );

//个人资料
Icon mineDataIco({double size = 18.0, Color color}) => Icon(
      const IconData(0xe641, fontFamily: 'iconfont'),
      size: size,
      color: color,
    );

//我的地址
Icon mineAddressIco({double size = 18.0, Color color}) => Icon(
      const IconData(0xe63e, fontFamily: 'iconfont'),
      size: size,
      color: color,
    );

//我的地址
Icon myNameIco({double size = 18.0, Color color}) => Icon(
      const IconData(0xe62d, fontFamily: 'iconfont'),
      size: size,
      color: color,
    );

//系统设置
Icon setupIco({double size = 18.0, Color color}) => Icon(
      const IconData(0xe60a, fontFamily: 'iconfont'),
      size: size,
      color: color,
    );

//支付宝
Icon aliPayIco({double size = 18.0, Color color}) => Icon(
      const IconData(0xe68a, fontFamily: 'iconfont'),
      size: size,
      color: color,
    );

//微信
Icon weChatIco({double size = 18.0, Color color}) => Icon(
      const IconData(0xe689, fontFamily: 'iconfont'),
      size: size,
      color: color,
    );

//自助上传
Icon mineUploadIco({double size = 18.0, Color color}) => Icon(
      const IconData(0xe61b, fontFamily: 'iconfont'),
      size: size,
      color: color,
    );

//自助上传
Icon conferenceIco({double size = 18.0, Color color}) => Icon(
      const IconData(0xe6e0, fontFamily: 'iconfont'),
      size: size,
      color: color,
    );

//打开摄像头
Icon cameraIco({double size = 18.0, Color color}) => Icon(
      const IconData(0xe600, fontFamily: 'iconfont'),
      size: size,
      color: color,
    );

//关机
Icon offIco({double size = 18.0, Color color}) => Icon(
      const IconData(0xe709, fontFamily: 'iconfont'),
      size: size,
      color: color,
    );

//举手
Icon handIco({double size = 18.0, Color color}) => Icon(
      const IconData(0xe6fa, fontFamily: 'iconfont'),
      size: size,
      color: color,
    );

//转发
Icon transpondIco({double size = 18.0, Color color}) => Icon(
      const IconData(0xe70f, fontFamily: 'iconfont'),
      size: size,
      color: color,
    );

//转发
Icon commentIco({double size = 18.0, Color color}) => Icon(
      const IconData(0xe627, fontFamily: 'iconfont'),
      size: size,
      color: color,
    );

//点赞
Icon likeIco({double size = 18.0, Color color}) => Icon(
      const IconData(0xe601, fontFamily: 'iconfont'),
      size: size,
      color: color,
    );

//省略
Icon omitIco({double size = 18.0, Color color}) => Icon(
      const IconData(0xe603, fontFamily: 'iconfont'),
      size: size,
      color: color,
    );

Icon communityIco({double size = 18.0, Color color}) => Icon(
      const IconData(0xe6b2, fontFamily: 'iconfont'),
      size: size,
      color: color,
    );

//WeChat
Icon weChatGoIco({double size = 18.0, Color color}) => Icon(
      const IconData(0xe61a, fontFamily: 'iconfont'),
      size: size,
      color: color,
    );

Icon friendIco({double size = 18.0, Color color}) => Icon(
      const IconData(0xe65b, fontFamily: 'iconfont'),
      size: size,
      color: color,
    );

Icon linkIco({double size = 18.0, Color color}) => Icon(
      const IconData(0xed26, fontFamily: 'iconfont'),
      size: size,
      color: color,
    );

//首页
Icon home1Ico({double size = 18.0, Color color}) => Icon(
      const IconData(0xe604, fontFamily: 'iconfont'),
      size: size,
      color: color,
    );

//首页
Icon visibilityIco({double size = 26.0, Color color}) => Icon(
      const IconData(0xe61d, fontFamily: 'iconfont'),
      size: size,
      color: color,
    );

//首页
Icon visibilityOffIco({double size = 18.0, Color color}) => Icon(
      const IconData(0xe61e, fontFamily: 'iconfont'),
      size: size,
      color: color,
    );
